# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.project
.settings/
.classpath

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Python 构建目录（避免影响前端 src/lib）
/lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Python 测试和覆盖率
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.coverage.*
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Docker
.dockerignore

# Docker 数据卷（开发环境）
docker-data/
volumes/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
config.local.py
settings.local.py
local_settings.py

# 上传文件
uploads/
media/
static/media/

# 缓存文件
.cache/
*.cache

# 编译输出
build/
dist/
*.min.js
*.min.css

# 测试文件
test-results/
coverage/
.nyc_output/

# 文档生成
docs/_build/
site/

# 备份文件
*.bak
*.backup
*.old
*.orig
*.tmp
*_backup.*

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 大文件
*.iso
*.dmg
*.pkg

# 机器学习模型文件
*.pkl
*.pickle
*.h5
*.model
models/
checkpoints/

# 数据文件
data/raw/
data/processed/
*.csv
*.json
*.xml
*.xlsx
*.xls

# 日志目录
logs/
log/

# 证书和密钥
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# 本地配置
.local
local.json
local.yml
local.yaml

# 编辑器配置（项目特定）
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# MacOS
.AppleDouble
.LSOverride

# Windows
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 项目特定忽略
# Neo4j 数据（如果不想提交数据库内容）
# data/neo4j/

# Milvus 数据
# data/milvus/

# 用户生成的内容
user_uploads/
user_data/

# 临时文件
*.tmp
*.temp
.temporary/

# 实验和测试
experiments/
playground/
scratch/

# 文档草稿
*.draft.md
draft_*

# 性能分析
*.prof
profile_*

# 监控和度量
metrics/
monitoring/