version: '3.8'

services:
  neo4j:
    image: neo4j:5.18
    container_name: neo4j-db
    ports:
      - 7474:7474   # HTTP Web UI
      - 7687:7687   # Bolt 协议端口
    environment:
      NEO4J_AUTH: neo4j/all-in-rag
      NEO4J_dbms_connector_bolt_advertised__address: localhost:7687
      # 允许从文件URL导入CSV
      NEO4J_dbms_security_allow__csv__import__from__file__urls: "true"
      # 设置导入目录
      NEO4J_dbms_directories_import: "/import"
      # 启用 APOC 插件
      NEO4J_PLUGINS: '["apoc"]'
      # APOC 配置
      NEO4J_apoc_export_file_enabled: "true"
      NEO4J_apoc_import_file_enabled: "true"
      NEO4J_apoc_import_file_use__neo4j__config: "true"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_plugins:/plugins
      # 将data文件夹映射到容器的import目录
      - ./data:/import
      # 映射脚本目录
      - ./data:/scripts
    restart: unless-stopped
    # 添加健康检查
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "all-in-rag", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 添加脚本执行服务
  neo4j-init:
    image: neo4j:5.18
    container_name: neo4j-init
    depends_on:
      neo4j:
        condition: service_healthy
    volumes:
      - ./data:/scripts
    command: >
      sh -c "
        echo 'Waiting for Neo4j to be ready...' &&
        sleep 10 &&
        echo 'Executing import script...' &&
        cypher-shell -a bolt://neo4j:7687 -u neo4j -p all-in-rag -f /scripts/neo4j_import.cypher &&
        echo 'Import completed!'
      "
    restart: "no"

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_plugins:
