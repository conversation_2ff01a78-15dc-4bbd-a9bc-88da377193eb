# 今天吃什么 - AI美食推荐助手
# Docker Compose 配置文件

services:
  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.11-community
    container_name: what-to-eat-neo4j
    environment:
      - NEO4J_AUTH=neo4j/all-in-rag
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use_neo4j_config=true
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - ./data/cypher:/import
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p all-in-rag 'RETURN 1'"]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 40s
    restart: unless-stopped

  # Milvus 向量数据库依赖服务
  milvus-etcd:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: what-to-eat-milvus-etcd
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  milvus-minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: what-to-eat-milvus-minio
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Milvus 向量数据库
  milvus-standalone:
    image: milvusdb/milvus:v2.3.2
    container_name: what-to-eat-milvus-standalone
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: milvus-etcd:2379
      MINIO_ADDRESS: milvus-minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      milvus-etcd:
        condition: service_healthy
      milvus-minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 60s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Neo4j 数据自动导入服务
  neo4j-init:
    image: neo4j:5.11-community
    container_name: what-to-eat-neo4j-init
    environment:
      - NEO4J_AUTH=neo4j/all-in-rag
    volumes:
      - ./data/cypher:/import
    depends_on:
      neo4j:
        condition: service_healthy
    command: >
      bash -c "
        echo '检查Neo4j数据库是否为空...'
        COUNT=$$(cypher-shell -a bolt://neo4j:7687 -u neo4j -p all-in-rag 'MATCH (n) RETURN count(n) as count' --format plain | tail -n 1)
        if [ \"$$COUNT\" = \"0\" ] && [ -f /import/neo4j_import.cypher ]; then
          echo '数据库为空，开始导入数据...'
          cypher-shell -a bolt://neo4j:7687 -u neo4j -p all-in-rag -f /import/neo4j_import.cypher
          echo '数据导入完成！'
        else
          echo '数据库已有数据或导入文件不存在，跳过导入'
        fi
      "
    restart: "no"

  # Python 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: what-to-eat-backend
    environment:
      - PYTHONPATH=/app
      - DOCKER_ENV=true
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=all-in-rag
      - MILVUS_HOST=milvus-standalone
      - MILVUS_PORT=19530
      # 模型配置
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-BAAI/bge-small-zh-v1.5}
      - LLM_MODEL=${LLM_MODEL:-moonshot-v1-8k}
      # 统一API配置
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.moonshot.cn/v1}
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./rag_modules:/app/rag_modules
      - ./config.py:/app/config.py
      - ./main.py:/app/main.py
      - ./requirements.txt:/app/requirements.txt
    depends_on:
      neo4j-init:
        condition: service_completed_successfully
      milvus-standalone:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    restart: unless-stopped

  # Next.js 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: runner
    container_name: what-to-eat-frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: what-to-eat-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      frontend:
        condition: service_healthy
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  etcd_data:
  minio_data:
  milvus_data:

networks:
  default:
    name: what-to-eat-network
