@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-gradient-to-br from-blue-50 via-white to-purple-50 min-h-screen;
  }

  * {
    @apply scroll-smooth;
  }
}

@layer components {
  /* 玻璃质感效果 */
  .glass {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-glass;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-lg border border-white/10 shadow-glass;
  }

  .glass-card {
    @apply glass rounded-2xl p-6 transition-all duration-300 hover:bg-white/15 hover:shadow-xl;
  }

  .glass-button {
    @apply glass rounded-xl px-4 py-2 transition-all duration-200 hover:bg-white/20 active:scale-95;
  }

  /* iOS风格按钮 */
  .ios-button {
    @apply bg-blue-500 text-white rounded-full px-6 py-3 font-medium transition-all duration-200 hover:bg-blue-600 active:scale-95 shadow-lg;
  }

  .ios-button-secondary {
    @apply bg-gray-100 text-gray-800 rounded-full px-6 py-3 font-medium transition-all duration-200 hover:bg-gray-200 active:scale-95 shadow-md;
  }

  /* 聊天界面样式 */
  .chat-message {
    @apply max-w-3xl mx-auto px-4 py-3 rounded-2xl shadow-sm transition-all duration-200;
  }

  /* 侧边栏布局过渡 */
  .sidebar-layout-transition {
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-open {
    @apply lg:ml-80;
  }

  .sidebar-closed {
    @apply ml-0;
  }

  .chat-message.user {
    @apply bg-blue-500 text-white ml-auto mr-4;
  }

  .chat-message.assistant {
    @apply glass mr-auto ml-4;
  }

  /* 食材清单样式 */
  .ingredient-item {
    @apply flex items-center space-x-3 p-3 rounded-xl transition-all duration-200 hover:bg-white/10;
  }

  .ingredient-checkbox {
    @apply w-5 h-5 rounded border-2 border-gray-300 transition-all duration-200 checked:bg-green-500 checked:border-green-500;
  }

  /* 烹饪步骤样式 */
  .cooking-step {
    @apply glass-card mb-4 relative overflow-hidden;
  }

  .cooking-step.active {
    @apply ring-2 ring-blue-500 bg-blue-50/20;
  }

  .cooking-step.completed {
    @apply bg-green-50/20 ring-2 ring-green-500;
  }

  /* 计时器样式 */
  .timer-display {
    @apply text-4xl font-mono font-bold text-center p-8 glass-card;
  }

  /* 加载动画 */
  .loading-dots {
    @apply inline-flex space-x-1;
  }

  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-bounce;
  }

  .loading-dots > div:nth-child(2) {
    animation-delay: 0.1s;
  }

  .loading-dots > div:nth-child(3) {
    animation-delay: 0.2s;
  }

  /* 渐变文字效果 */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* 自定义滚动条 */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}